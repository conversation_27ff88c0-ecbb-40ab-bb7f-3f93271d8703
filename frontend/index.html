<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>多圖辨識編輯器（支援台股和美股）</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
  <script src="js/logger.js"></script>
  <style>
    :root {
      --primary-color: #3498db;
      --primary-dark: #2980b9;
      --secondary-color: #2ecc71;
      --secondary-dark: #27ae60;
      --danger-color: #e74c3c;
      --danger-dark: #c0392b;
      --text-color: #333;
      --light-bg: #f8f9fa;
      --border-color: #e0e0e0;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --radius: 8px;
      --transition: all 0.3s ease;
    }
    
    * {
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Noto Sans TC', sans-serif;
      margin: 0;
      padding: 20px;
      display: flex;
      gap: 40px;
      background-color: #f0f2f5;
      color: var(--text-color);
      min-height: 100vh;
    }
    
    #sidebar {
      width: 300px;
      background-color: white;
      border-radius: var(--radius);
      padding: 20px;
      box-shadow: var(--shadow);
      height: fit-content;
      display: flex;
      flex-direction: column;
    }
    
    .user-panel {
      margin-bottom: 20px;
      padding: 15px;
      background-color: var(--light-bg);
      border-radius: var(--radius);
      border: 1px solid var(--border-color);
    }
    
    .user-panel .user-info {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
    }
    
    .user-panel .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: var(--primary-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 18px;
    }
    
    .user-panel .user-details {
      flex-grow: 1;
    }
    
    .user-panel .username {
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
    }
    
    .user-panel .role {
      font-size: 12px;
      color: #666;
      margin: 0;
    }
    
    .user-panel .role.admin {
      color: var(--secondary-dark);
    }
    
    .user-panel .user-actions {
      display: flex;
      gap: 10px;
    }
    
    .user-panel .btn {
      padding: 8px 12px;
      border: none;
      border-radius: var(--radius);
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .user-panel .btn-primary {
      background-color: var(--primary-color);
      color: white;
    }
    
    .user-panel .btn-primary:hover {
      background-color: var(--primary-dark);
    }
    
    .user-panel .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }
    
    .user-panel .btn-danger:hover {
      background-color: var(--danger-dark);
    }
    
    #mainArea {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: white;
      border-radius: var(--radius);
      padding: 20px;
      box-shadow: var(--shadow);
      max-width: 1200px;
      margin: 0 auto;
    }
    
    canvas {
      border: 1px solid var(--border-color);
      border-radius: var(--radius);
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    #editor, #navigation, #modeSelector {
      display: flex;
      gap: 15px;
      margin: 15px 0;
      align-items: center;
      width: 100%;
      justify-content: center;
    }
    
    input[type="color"], input[type="number"], input[type="text"] {
      width: 100px;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: var(--radius);
      font-family: inherit;
      transition: var(--transition);
    }
    
    input[type="color"] {
      height: 38px;
      padding: 2px;
    }
    
    input[type="color"]:focus, input[type="number"]:focus, input[type="text"]:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }
    
    label {
      display: flex;
      flex-direction: column;
      gap: 5px;
      font-weight: 500;
    }
    
    #navigation {
      background-color: var(--light-bg);
      padding: 10px 20px;
      border-radius: var(--radius);
      width: auto;
    }
    
    #navigation button {
      font-size: 18px;
      padding: 8px 16px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius);
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    #navigation button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }
    
    #canvasIndexLabel {
      font-size: 18px;
      font-weight: 500;
      padding: 0 15px;
      color: var(--primary-dark);
    }

    /* 連結顯示區域樣式 */
    #linkArea {
      margin: 15px 0;
      padding: 10px;
      background: var(--light-bg);
      border-radius: var(--radius);
      border: 1px solid var(--border-color);
    }



    .link-item {
      margin: 8px 0;
      padding: 8px;
      background: white;
      border-radius: 4px;
      border-left: 4px solid var(--primary-color);
    }

    .link-label {
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 4px;
      font-size: 14px;
    }

    .link-url {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #666;
      word-break: break-all;
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 3px;
      border: 1px solid #ddd;
    }

    .link-url a {
      color: #3498db;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .link-url a:hover {
      color: #2980b9;
      text-decoration: underline;
    }

    .link-url a:visited {
      color: #8e44ad;
    }

    .link-item.news {
      border-left-color: #ff6b35;
    }

    .link-item.report {
      border-left-color: #2ecc71;
    }

    .link-item.company {
      border-left-color: #9b59b6;
    }

    #modeSelector {
      background: var(--light-bg);
      padding: 15px;
      border-radius: var(--radius);
      margin-bottom: 20px;
      justify-content: center;
      width: auto;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    #modeSelector button {
      padding: 10px 20px;
      border: none;
      border-radius: var(--radius);
      background: #e0e0e0;
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
    }
    
    #modeSelector button:hover {
      background: #d0d0d0;
    }
    
    #modeSelector button.active {
      background: var(--primary-color);
      color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .file-group {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid var(--border-color);
      border-radius: var(--radius);
      background-color: var(--light-bg);
      transition: var(--transition);
    }
    
    .file-group:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .file-group h4 {
      margin: 0 0 15px 0;
      color: var(--primary-dark);
      font-weight: 600;
      border-bottom: 2px solid var(--primary-color);
      padding-bottom: 8px;
    }

    .file-requirement {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 8px 12px;
      margin-bottom: 10px;
      font-size: 13px;
      color: #856404;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .file-status {
      font-size: 12px;
      margin-top: 5px;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: 500;
    }

    .file-status.incomplete {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .file-status.complete {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    input[type="file"] {
      width: 100%;
      padding: 8px 0;
      margin-bottom: 10px;
    }
    
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 10px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .message {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 25px;
      border-radius: var(--radius);
      color: white;
      font-weight: bold;
      z-index: 1000;
      transform: translateX(100%);
      transition: transform 0.3s ease, opacity 0.3s ease;
      box-shadow: var(--shadow);
      opacity: 0;
    }
    
    .message.success {
      background-color: var(--secondary-color);
    }
    
    .message.error {
      background-color: var(--danger-color);
    }
    
    .message.show {
      transform: translateX(0);
      opacity: 1;
    }
    
    .btn-recognize {
      width: 100%;
      padding: 10px;
      background-color: var(--secondary-color);
      color: white;
      border: none;
      border-radius: var(--radius);
      cursor: pointer;
      transition: var(--transition);
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    
    .btn-recognize:hover {
      background-color: var(--secondary-dark);
      transform: translateY(-2px);
    }
    
    .btn-recognize:disabled {
      background-color: #95a5a6;
      cursor: not-allowed;
      transform: none;
    }
    
    .export-controls {
      margin-top: 30px;
      padding: 15px;
      border: 1px solid var(--border-color);
      border-radius: var(--radius);
      background-color: var(--light-bg);
      transition: var(--transition);
    }
    
    .export-controls:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .export-controls h4 {
      margin: 0 0 15px 0;
      color: var(--primary-dark);
      font-weight: 600;
      border-bottom: 2px solid var(--primary-color);
      padding-bottom: 8px;
    }
    
    .btn-export {
      width: 100%;
      padding: 10px;
      margin-bottom: 8px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--radius);
      cursor: pointer;
      transition: var(--transition);
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    
    .btn-export:last-child {
      margin-bottom: 0;
    }
    
    .btn-export:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: var(--radius);
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 5px;
      margin-bottom: 5px;
    }

    .btn-secondary:hover {
      background-color: #5a6268;
      transform: translateY(-1px);
    }
    
    #editor {
      background-color: var(--light-bg);
      padding: 15px;
      border-radius: var(--radius);
      flex-wrap: wrap;
      justify-content: center;
    }
    
    @media (max-width: 768px) {
      body {
        flex-direction: column;
        padding: 10px;
      }
      
      #sidebar {
        width: 100%;
        margin-bottom: 20px;
      }
      
      #editor {
        flex-direction: column;
      }
      
      input[type="color"], input[type="number"], input[type="text"] {
        width: 100%;
      }
      
      #navigation button {
        padding: 8px 12px;
        font-size: 16px;
      }
      
      #canvasIndexLabel {
        font-size: 16px;
      }
    }
  </style>
</head>
<body>

<div id="sidebar">
  <div class="user-panel" id="userPanel">
    <!-- 用戶資訊將由JavaScript動態生成 -->
  </div>
  
  <div class="file-group">
    <h4><i class="fas fa-chart-line"></i> 台股文字檔</h4>
    <div class="file-requirement">
      <i class="fas fa-info-circle"></i>
      請上傳 2 個檔案：1個 .txt 檔案 + 1個 .docx 檔案
    </div>
    <input type="file" id="taiexUploader" multiple accept=".txt,.doc,.docx">
    <div id="taiexFileStatus" class="file-status incomplete">
      已選擇 0 個檔案，還需要 2 個檔案
    </div>
    <button id="taiexRecognizeBtn" onclick="startRecognition('taiex')" class="btn-recognize">
      <i class="fas fa-magic"></i> 辨識台股
    </button>
  </div>
  
  <div class="file-group">
    <h4><i class="fas fa-chart-bar"></i> 美股文字檔</h4>
    <div class="file-requirement">
      <i class="fas fa-info-circle"></i>
      請上傳 2 個檔案：1個 .txt 檔案 + 1個 .docx 檔案
    </div>
    <input type="file" id="ussUploader" multiple accept=".txt,.doc,.docx">
    <div id="ussFileStatus" class="file-status incomplete">
      已選擇 0 個檔案，還需要 2 個檔案
    </div>
    <button id="ussRecognizeBtn" onclick="startRecognition('uss')" class="btn-recognize">
      <i class="fas fa-magic"></i> 辨識美股
    </button>
  </div>
  
  <div class="export-controls">
    <h4><i class="fas fa-download"></i> 匯出圖片</h4>
    <button onclick="exportPNGs('taiex')" class="btn-export">
      <i class="fas fa-file-export"></i> 匯出台股圖片
    </button>
    <button onclick="exportPNGs('uss')" class="btn-export">
      <i class="fas fa-file-export"></i> 匯出美股圖片
    </button>
    <button onclick="exportPNGs('all')" class="btn-export">
      <i class="fas fa-file-export"></i> 匯出所有圖片
    </button>
  </div>
  
  <div class="export-controls">
    <h4><i class="fas fa-link"></i> 下載連結資料</h4>
    <button onclick="downloadLinks('taiex')" class="btn-export">
      <i class="fas fa-download"></i> 下載台股連結
    </button>
    <button onclick="downloadLinks('uss')" class="btn-export">
      <i class="fas fa-download"></i> 下載美股連結
    </button>
    <button onclick="downloadLinks('all')" class="btn-export">
      <i class="fas fa-download"></i> 下載全部連結
    </button>
  </div>
</div>

<div id="mainArea">
  <div id="modeSelector">
    <button id="taiexBtn" class="active" onclick="switchMode('taiex')">
      <i class="fas fa-chart-line"></i> 台股模式
    </button>
    <button id="ussBtn" onclick="switchMode('uss')">
      <i class="fas fa-chart-bar"></i> 美股模式
    </button>
  </div>

  <canvas id="mainCanvas" width="306" height="522"></canvas>

  <div id="navigation">
    <button onclick="prevCanvas()"><i class="fas fa-chevron-left"></i> 上一張</button>
    <span id="canvasIndexLabel">第 1 張</span>
    <button onclick="nextCanvas()">下一張 <i class="fas fa-chevron-right"></i></button>
  </div>

  <!-- 連結顯示區域 -->
  <div id="linkArea" style="display: none;">
    <div id="linkContent"></div>
  </div>

  <div id="editor">
    <label>
      <span><i class="fas fa-font"></i> 文字</span>
      <input type="text" id="textEdit" placeholder="輸入文字">
    </label>
    <label>
      <span><i class="fas fa-text-height"></i> 大小</span>
      <input type="number" id="fontSizeEdit" min="10" max="100" value="24">
    </label>
    <label>
      <span><i class="fas fa-palette"></i> 顏色</span>
      <input type="color" id="colorEdit" value="#000000">
    </label>
  </div>
</div>

<script>
  // 檢查用戶是否已登入
  const currentUser = JSON.parse(sessionStorage.getItem('loginStatus') || 'null');
  if (!currentUser) {
    // 未登入，重定向到登入頁面
    window.location.href = 'login.html';
  }
  
  // 更新用戶面板
  function updateUserPanel() {
    const userPanel = document.getElementById('userPanel');
    
    if (currentUser) {
      const firstLetter = currentUser.username.charAt(0).toUpperCase();
      const isAdmin = currentUser.role === 'admin';
      
      userPanel.innerHTML = `
        <div class="user-info">
          <div class="user-avatar">${firstLetter}</div>
          <div class="user-details">
            <p class="username">${currentUser.username}</p>
            <p class="role ${isAdmin ? 'admin' : ''}">${isAdmin ? '管理員' : '一般用戶'}</p>
          </div>
        </div>
        <div class="user-actions">
          <button class="btn btn-secondary" id="changePasswordBtn"><i class="fas fa-key"></i> 更改密碼</button>
          ${isAdmin ? '<button class="btn btn-primary" onclick="window.location.href=\'settings.html\'"><i class="fas fa-cog"></i> 設定</button>' : ''}
          <button class="btn btn-danger" id="logoutBtn"><i class="fas fa-sign-out-alt"></i> 登出</button>
        </div>
      `;
      
      // 添加更改密碼事件監聽器
      document.getElementById('changePasswordBtn').addEventListener('click', () => {
        showChangePasswordModal();
      });

      // 添加登出事件監聽器
      document.getElementById('logoutBtn').addEventListener('click', async () => {
        // 記錄登出事件
        try {
          if (window.Logger) {
            await Logger.logLogout();
          }
        } catch (logError) {
          console.error(logError);
        }

        sessionStorage.removeItem('loginStatus');
        window.location.href = 'login.html';
      });
    }
  }
  
  // 初始化用戶面板
  updateUserPanel();
  
  const canvas = new fabric.Canvas('mainCanvas');
  let currentMode = 'taiex'; // 'taiex' or 'uss'
  let currentIndex = 0;
  let activeObject = null;
  let linkData = { taiex: [], uss: [] }; // 儲存連結資料
  
  // 載入連結資料
  async function loadLinkData() {
    try {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const dateFolder = `${year}${month}${day}`;

      const apiBaseUrl = 'https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7';

      // 載入台股連結
      try {
        const taiexResponse = await fetch(`${apiBaseUrl}/get_url_json?type=taiex&date=${dateFolder}`);
        if (taiexResponse.ok) {
          linkData.taiex = await taiexResponse.json();
          console.log('台股連結載入成功:', linkData.taiex);
        } else {
          console.log('台股連結載入失敗，狀態碼:', taiexResponse.status);
        }
      } catch (error) {
        console.log('載入台股連結失敗:', error);
      }

      // 載入美股連結
      try {
        const ussResponse = await fetch(`${apiBaseUrl}/get_url_json?type=us&date=${dateFolder}`);
        if (ussResponse.ok) {
          linkData.uss = await ussResponse.json();
          console.log('美股連結載入成功:', linkData.uss);
        } else {
          console.log('美股連結載入失敗，狀態碼:', ussResponse.status);
        }
      } catch (error) {
        console.log('載入美股連結失敗:', error);
      }

      // 載入完成後顯示當前頁面的連結
      displayCurrentLinks();

    } catch (error) {
      console.error('載入連結資料失敗:', error);
    }
  }



  // 顯示當前頁面的連結
  function displayCurrentLinks() {
    const linkArea = document.getElementById('linkArea');
    const linkContent = document.getElementById('linkContent');

    // 獲取當前模式的連結資料
    const currentLinks = linkData[currentMode];

    console.log('displayCurrentLinks called:', {
      currentMode,
      currentIndex,
      currentLinks: currentLinks ? currentLinks.length : 'null',
      linkData
    });

    if (!currentLinks || currentLinks.length === 0) {
      console.log('No links found, hiding link area');
      linkArea.style.display = 'none';
      return;
    }

    linkContent.innerHTML = '';

    // 判斷當前頁面類型
    if (currentIndex < 4) {
      // 前4張：總體市場頁面，不顯示連結
      linkArea.style.display = 'none';
      return;
    } else {
      // 第5-8張：各股焦點，顯示對應的連結
      const linkIndex = currentIndex - 4; // 第5張對應index 0，第6張對應index 1，以此類推
      const currentPageLink = currentLinks[linkIndex];

      if (!currentPageLink) {
        console.log('No link for current page, hiding link area');
        linkArea.style.display = 'none';
        return;
      }

      // 創建當前頁面標題
      const pageTitle = document.createElement('div');
      pageTitle.style.cssText = `
        font-weight: bold;
        color: #3498db;
        margin: 0 0 15px 0;
        padding: 8px 12px;
        background: #e3f2fd;
        border-radius: 4px;
        border-left: 4px solid #3498db;
        text-align: center;
      `;
      pageTitle.textContent = `第 ${currentIndex + 1} 張 (各股焦點)`;
      linkContent.appendChild(pageTitle);

      // 顯示完整新聞連結
      if (currentPageLink.news_url) {
        const newsItem = document.createElement('div');
        newsItem.className = 'link-item news';
        newsItem.innerHTML = `
          <div class="link-label">完整新聞</div>
          <div class="link-url"><a href="${currentPageLink.news_url}" target="_blank" rel="noopener noreferrer">${currentPageLink.news_url}</a></div>
        `;
        linkContent.appendChild(newsItem);
      }

      // 顯示其他連結（投顧報告或企業）
      if (currentPageLink.url && currentPageLink.url !== currentPageLink.news_url) {
        const otherItem = document.createElement('div');

        // 判斷連結類型
        let linkType = 'company';
        let linkLabel = '企業';

        if (currentPageLink.url.includes('investmentuat.kgisia.com.tw')) {
          linkType = 'report';
          linkLabel = '投顧報告';
        }

        otherItem.className = `link-item ${linkType}`;
        otherItem.innerHTML = `
          <div class="link-label">${linkLabel}</div>
          <div class="link-url"><a href="${currentPageLink.url}" target="_blank" rel="noopener noreferrer">${currentPageLink.url}</a></div>
        `;
        linkContent.appendChild(otherItem);
      }
    }

    linkArea.style.display = 'block';
  }

  // 檢查當天的 JSON 文件
  async function checkTodayJson() {
    // 獲取 canvas 元素的位置和尺寸
    const canvas = document.getElementById('mainCanvas');
    const canvasRect = canvas.getBoundingClientRect();
    
    // 創建一個覆蓋整個 canvas 的半透明灰色背景
    const overlay = document.createElement('div');
    overlay.id = 'canvasOverlay';
    overlay.style.position = 'absolute';
    overlay.style.top = `${canvasRect.top}px`;
    overlay.style.left = `${canvasRect.left}px`;
    overlay.style.width = `${canvasRect.width}px`;
    overlay.style.height = `${canvasRect.height}px`;
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '999';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    
    // 創建加載指示器
    const loadingIndicator = document.createElement('div');
    loadingIndicator.id = 'todayJsonLoading';
    loadingIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    loadingIndicator.style.color = 'white';
    loadingIndicator.style.padding = '20px';
    loadingIndicator.style.borderRadius = 'var(--radius)';
    loadingIndicator.style.display = 'flex';
    loadingIndicator.style.flexDirection = 'column';
    loadingIndicator.style.alignItems = 'center';
    loadingIndicator.style.gap = '10px';
    
    const spinner = document.createElement('div');
    spinner.className = 'loading';
    spinner.style.width = '30px';
    spinner.style.height = '30px';
    spinner.style.borderWidth = '4px';
    
    const text = document.createElement('div');
    text.textContent = '正在載入今日數據...';
    text.style.fontWeight = '500';
    
    loadingIndicator.appendChild(spinner);
    loadingIndicator.appendChild(text);
    overlay.appendChild(loadingIndicator);
    document.body.appendChild(overlay);
    
    try {
      // 使用 API 端點檢查當天的 JSON 文件
      const response = await fetch('https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7/check_today_json');
      
      if (!response.ok) {
        showMessage('載入今日數據失敗', 'error');
        return;
      }
      
      const result = await response.json();
      
      // 如果有台灣股市數據，則載入並應用
      if (result.taiwan_exists && result.taiwan_data) {
        configs.taiex.defaultDatas = result.taiwan_data;
        applyRecognitionResult('taiex');
        showMessage('成功載入數據', 'success');
      }
      
      // 如果有美國股市數據，則載入並應用
      if (result.us_exists && result.us_data) {
        configs.uss.defaultDatas = result.us_data;
        applyRecognitionResult('uss');
        showMessage('成功載入數據', 'success');
      }
      
      // 如果沒有數據
      if ((!result.taiwan_exists || !result.taiwan_data) && (!result.us_exists || !result.us_data)) {
        showMessage('今日數據尚未產生', 'error');
      }
    } catch (error) {
      showMessage('載入今日數據失敗', 'error');
    } finally {
      // 移除加載指示器和覆蓋層
      const overlay = document.getElementById('canvasOverlay');
      if (overlay && overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    }
  }

  // 兩套不同的資料配置
  const configs = {
    taiex: {
      totalCanvases: 8, // 前4張 + 後4張taiex5變體
      imagePaths: [
        "taiex1.jpg",
        "taiex2.jpg", 
        "taiex3.jpg",
        "taiex4.jpg",
        "taiex5.jpg",
        "taiex5.jpg",
        "taiex5.jpg",
        "taiex5.jpg"
      ],
      // 用於根據image_index動態選擇底圖的配置
      dynamicImagePaths: {
        4: { 0: "taiex5.jpg", 1: "taiex5_1.jpg" },
        5: { 0: "taiex5.jpg", 1: "taiex5_1.jpg" },
        6: { 0: "taiex5.jpg", 1: "taiex5_1.jpg" },
        7: { 0: "taiex5.jpg", 1: "taiex5_1.jpg" }
      },
      canvasData: [],
      uploadedFiles: [],
      defaultDatas: [],
      imageMap: {
        up: "taiex_up.png",
        down: "taiex_down.png"
      }
    },
    uss: {
      totalCanvases: 8, // 前4張 + 後4張uss5變體
      imagePaths: [
        "uss1.jpg",
        "uss2.jpg",
        "uss3.jpg", 
        "uss4.jpg",
        "uss5.jpg",
        "uss5.jpg",
        "uss5.jpg",
        "uss5.jpg"
      ],
      // 用於根據image_index動態選擇底圖的配置
      dynamicImagePaths: {
        4: { 0: "uss5.jpg", 1: "uss5_1.jpg", 2: "uss5_2.jpg" },
        5: { 0: "uss5.jpg", 1: "uss5_1.jpg", 2: "uss5_2.jpg" },
        6: { 0: "uss5.jpg", 1: "uss5_1.jpg", 2: "uss5_2.jpg" },
        7: { 0: "uss5.jpg", 1: "uss5_1.jpg", 2: "uss5_2.jpg" }
      },
      canvasData: [],
      uploadedFiles: [],
      defaultDatas: [],
      imageMap: {
        us_up: "us_up.png",
        us_down: "us_down.png"
      }
    }
  };

  const canvasSizes = [
    { width: 306, height: 522 },
    { width: 306, height: 522 },
    { width: 306, height: 522 },
    { width: 306, height: 522 },
    { width: 306, height: 522 },
    { width: 306, height: 522 },
    { width: 306, height: 522 },
    { width: 306, height: 522 }
  ];

  configs.taiex.canvasData = new Array(configs.taiex.totalCanvases).fill(null).map(() => []);
  configs.uss.canvasData = new Array(configs.uss.totalCanvases).fill(null).map(() => []);

  configs.taiex.canvasImageIndex = new Array(configs.taiex.totalCanvases).fill(0);
  configs.uss.canvasImageIndex = new Array(configs.uss.totalCanvases).fill(0);

  function getCurrentConfig() {
    return configs[currentMode];
  }

  function switchMode(mode) {
    const currentConfig = getCurrentConfig();
    if (currentConfig.canvasData[currentIndex]) {
      currentConfig.canvasData[currentIndex] = canvas.getObjects().map(obj => fabric.util.object.clone(obj));
    }

    currentMode = mode;

    document.getElementById('taiexBtn').classList.toggle('active', mode === 'taiex');
    document.getElementById('ussBtn').classList.toggle('active', mode === 'uss');
    
    const newConfig = getCurrentConfig();
    if (currentIndex >= newConfig.totalCanvases) {
      currentIndex = 0;
    }
    loadCanvas(currentIndex);
  }

  function loadCanvas(index) {
    const config = getCurrentConfig();
    let bgUrl = config.imagePaths[index];
    
    // 如果是第5-8張圖片且有動態路徑配置，根據image_index選擇底圖
    if (config.dynamicImagePaths && config.dynamicImagePaths[index]) {
      const imageIndex = config.canvasImageIndex[index];
      const dynamicPath = config.dynamicImagePaths[index][imageIndex];
      if (dynamicPath) {
        bgUrl = dynamicPath;
      }
    }
    
    const { width, height } = canvasSizes[index];

    const canvasEl = document.getElementById('mainCanvas');
    canvasEl.width = width;
    canvasEl.height = height;

    canvas.setWidth(width);
    canvas.setHeight(height);

    fabric.Image.fromURL(bgUrl, function(img) {
      const scaleX = width / img.width;
      const scaleY = height / img.height;

      canvas.clear();

      canvas.setBackgroundImage(img, () => {
        if (config.canvasData[index]) {
          config.canvasData[index].forEach(obj => canvas.add(obj));
        } else {
          config.canvasData[index] = [];
        }

        canvas.renderAll();
      }, {
        scaleX,
        scaleY
      });

      document.getElementById('canvasIndexLabel').innerText = `第 ${index + 1} 張 (${currentMode === 'taiex' ? '台股' : '美股'})`;

      // 顯示當前頁面的連結
      displayCurrentLinks();
    }, { crossOrigin: 'anonymous' });
  }

  function showCanvas(index) {
    const config = getCurrentConfig();
    if (config.canvasData[currentIndex]) {
      config.canvasData[currentIndex] = canvas.getObjects().map(obj => fabric.util.object.clone(obj));
    }
    currentIndex = index;
    loadCanvas(currentIndex);
  }

  function nextCanvas() {
    const config = getCurrentConfig();
    const nextIndex = (currentIndex + 1) % config.totalCanvases;
    showCanvas(nextIndex);
  }

  function prevCanvas() {
    const config = getCurrentConfig();
    const prevIndex = (currentIndex - 1 + config.totalCanvases) % config.totalCanvases;
    showCanvas(prevIndex);
  }

  const textEdit = document.getElementById('textEdit');
  const fontSizeEdit = document.getElementById('fontSizeEdit');
  const colorEdit = document.getElementById('colorEdit');

  canvas.on('selection:created', updateEditor);
  canvas.on('selection:updated', updateEditor);
  canvas.on('selection:cleared', () => {
    activeObject = null;
    textEdit.value = '';
    fontSizeEdit.value = '';
    colorEdit.value = '#000000';
  });

  function updateEditor() {
    activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === 'textbox') {
      textEdit.value = activeObject.text;
      fontSizeEdit.value = activeObject.fontSize;
      colorEdit.value = activeObject.fill;
    }
  }

  textEdit.addEventListener('input', () => {
    if (activeObject) {
      activeObject.text = textEdit.value;
      canvas.renderAll();
    }
  });

  fontSizeEdit.addEventListener('input', () => {
    if (activeObject) {
      activeObject.set({ fontSize: parseInt(fontSizeEdit.value) });
      canvas.renderAll();
    }
  });

  colorEdit.addEventListener('input', () => {
    if (activeObject) {
      activeObject.set({ fill: colorEdit.value });
      canvas.renderAll();
    }
  });

  function exportPNGs(exportType) {
    let exportConfig = [];
    let confirmMessage = '';
    
    if (exportType === 'taiex') {
      exportConfig = [{ mode: 'taiex', config: configs.taiex }];
      confirmMessage = '你確定要匯出台股 PNG 檔案嗎？';
    } else if (exportType === 'uss') {
      exportConfig = [{ mode: 'uss', config: configs.uss }];
      confirmMessage = '你確定要匯出美股 PNG 檔案嗎？';
    } else if (exportType === 'all') {
      exportConfig = [
        { mode: 'taiex', config: configs.taiex },
        { mode: 'uss', config: configs.uss }
      ];
      confirmMessage = '你確定要匯出所有 PNG 檔案嗎？';
    }

    const confirmed = confirm(confirmMessage);
    if (!confirmed) return;

    const originalMode = currentMode;
    const originalIndex = currentIndex;

    const currentConfig = getCurrentConfig();
    if (currentConfig.canvasData[currentIndex]) {
      currentConfig.canvasData[currentIndex] = canvas.getObjects().map(obj => fabric.util.object.clone(obj));
    }

    let exportIndex = 0;
    
    const exportNext = () => {
      if (exportIndex >= exportConfig.length) {
        currentMode = originalMode;
        showCanvas(originalIndex);
        return;
      }
      
      const { mode, config } = exportConfig[exportIndex];
      currentMode = mode;

      const exportCanvasNext = (canvasIndex) => {
        if (canvasIndex >= config.totalCanvases) {
          exportIndex++;
          exportNext();
          return;
        }
        
        showCanvas(canvasIndex);
        setTimeout(() => {
          // 獲取當前 canvas 尺寸
          const currentWidth = canvas.getWidth();
          const currentHeight = canvas.getHeight();

          // 設定固定匯出尺寸
          const exportHeight = 2400;
          const exportWidth = 1387;

          // 計算縮放比例 - 分別計算背景和物件的縮放
          const scaleX = exportWidth / currentWidth;
          const scaleY = exportHeight / currentHeight;
          const scale = Math.min(scaleX, scaleY); // 物件使用較小縮放保持比例

          // 背景圖片使用較大的縮放比例以確保完全覆蓋，避免白邊
          const bgScale = Math.max(exportWidth / currentWidth, exportHeight / currentHeight);

          // 調試資訊（可選）
          console.log(`匯出尺寸: ${exportWidth}x${exportHeight}, 縮放比例: ${scale.toFixed(2)}`);

          // 創建新的 canvas 用於匯出，使用計算出的尺寸
          const exportCanvas = new fabric.Canvas(document.createElement('canvas'), {
            width: exportWidth,
            height: exportHeight,
            backgroundColor: 'white'
          });

          // 獲取當前背景圖片
          const backgroundImage = canvas.backgroundImage;

          if (backgroundImage) {
            // 複製背景圖片到匯出 canvas
            backgroundImage.clone((clonedBg) => {
              // 背景圖片使用統一的較大縮放比例以完全覆蓋區域
              clonedBg.scaleX = (clonedBg.scaleX || 1) * bgScale;
              clonedBg.scaleY = (clonedBg.scaleY || 1) * bgScale;

              // 確保背景圖片居中
              clonedBg.left = 0;
              clonedBg.top = 0;

              exportCanvas.setBackgroundImage(clonedBg, () => {
                // 複製所有物件到新 canvas 並縮放
                const objects = canvas.getObjects();
                const promises = objects.map(obj => {
                  return new Promise((resolve) => {
                    obj.clone((cloned) => {
                      // 按比例縮放物件的尺寸和位置
                      cloned.scaleX = (cloned.scaleX || 1) * scale;
                      cloned.scaleY = (cloned.scaleY || 1) * scale;
                      cloned.left = (cloned.left || 0) * scale;
                      cloned.top = (cloned.top || 0) * scale;

                      exportCanvas.add(cloned);
                      resolve();
                    });
                  });
                });

                Promise.all(promises).then(() => {
                  exportCanvas.renderAll();
                  const dataURL = exportCanvas.toDataURL({
                    format: 'png',
                    quality: 1.0
                  });

                  const link = document.createElement('a');
                  link.href = dataURL;

                  // 生成檔名：台股 twYYYYMMDD_01，美股 usYYYYMMDD_01
                  const today = new Date();
                  const year = today.getFullYear();
                  const month = String(today.getMonth() + 1).padStart(2, '0');
                  const day = String(today.getDate()).padStart(2, '0');
                  const dateStr = `${year}${month}${day}`;

                  const prefix = mode === 'taiex' ? 'tw' : 'us';
                  const fileNumber = String(canvasIndex + 1).padStart(2, '0');
                  link.download = `${prefix}${dateStr}_${fileNumber}.png`;

                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);

                  // 清理匯出用的 canvas
                  exportCanvas.dispose();

                  exportCanvasNext(canvasIndex + 1);
                });
              }, {
                scaleX: clonedBg.scaleX,
                scaleY: clonedBg.scaleY,
                left: clonedBg.left,
                top: clonedBg.top
              });
            });
          } else {
            // 如果沒有背景圖片，直接處理物件
            const objects = canvas.getObjects();
            const promises = objects.map(obj => {
              return new Promise((resolve) => {
                obj.clone((cloned) => {
                  // 按比例縮放物件的尺寸和位置
                  cloned.scaleX = (cloned.scaleX || 1) * scale;
                  cloned.scaleY = (cloned.scaleY || 1) * scale;
                  cloned.left = (cloned.left || 0) * scale;
                  cloned.top = (cloned.top || 0) * scale;

                  exportCanvas.add(cloned);
                  resolve();
                });
              });
            });

            Promise.all(promises).then(() => {
              exportCanvas.renderAll();
              const dataURL = exportCanvas.toDataURL({
                format: 'png',
                quality: 1.0
              });

              const link = document.createElement('a');
              link.href = dataURL;

              // 生成檔名：台股 twYYYYMMDD_01，美股 usYYYYMMDD_01
              const today = new Date();
              const year = today.getFullYear();
              const month = String(today.getMonth() + 1).padStart(2, '0');
              const day = String(today.getDate()).padStart(2, '0');
              const dateStr = `${year}${month}${day}`;

              const prefix = mode === 'taiex' ? 'tw' : 'us';
              const fileNumber = String(canvasIndex + 1).padStart(2, '0');
              link.download = `${prefix}${dateStr}_${fileNumber}.png`;

              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // 清理匯出用的 canvas
              exportCanvas.dispose();

              exportCanvasNext(canvasIndex + 1);
            });
          }
        }, 500);
      };
      
      exportCanvasNext(0);
    };

    exportNext();
  }

  document.getElementById('taiexUploader').addEventListener('change', async (e) => {
    configs.taiex.uploadedFiles = Array.from(e.target.files);
    updateFileStatus('taiex');

    // 記錄上傳台指文字檔事件
    try {
      if (window.Logger) {
        await Logger.logUploadTaiex(configs.taiex.uploadedFiles);
      }
    } catch (logError) {
      console.error(logError);
    }
  });

  document.getElementById('ussUploader').addEventListener('change', async (e) => {
    configs.uss.uploadedFiles = Array.from(e.target.files);
    updateFileStatus('uss');

    // 記錄上傳美股文字檔事件
    try {
      if (window.Logger) {
        await Logger.logUploadUS(configs.uss.uploadedFiles);
      }
    } catch (logError) {
      console.error(logError);
    }
  });

  // 更新檔案狀態顯示
  function updateFileStatus(mode) {
    const config = configs[mode];
    const statusElement = document.getElementById(`${mode}FileStatus`);
    const fileCount = config.uploadedFiles.length;

    if (fileCount === 0) {
      statusElement.textContent = '已選擇 0 個檔案，還需要 2 個檔案';
      statusElement.className = 'file-status incomplete';
    } else if (fileCount === 1) {
      statusElement.textContent = '已選擇 1 個檔案，還需要 1 個檔案';
      statusElement.className = 'file-status incomplete';
    } else if (fileCount === 2) {
      // 檢查檔案類型
      const hasText = config.uploadedFiles.some(file => file.name.toLowerCase().endsWith('.txt'));
      const hasDocx = config.uploadedFiles.some(file => file.name.toLowerCase().endsWith('.docx'));

      if (hasText && hasDocx) {
        statusElement.textContent = '✓ 已選擇 2 個檔案（1個 .txt + 1個 .docx）';
        statusElement.className = 'file-status complete';
      } else {
        statusElement.textContent = '已選擇 2 個檔案，但需要 1個 .txt + 1個 .docx';
        statusElement.className = 'file-status incomplete';
      }
    } else {
      statusElement.textContent = `已選擇 ${fileCount} 個檔案，請只選擇 2 個檔案（1個 .txt + 1個 .docx）`;
      statusElement.className = 'file-status incomplete';
    }
  }

  async function startRecognition(mode) {
    const config = configs[mode];

    // 檢查檔案數量
    if (config.uploadedFiles.length === 0) {
      showMessage(`請先上傳 ${mode === 'taiex' ? '台股' : '美股'} 文字檔`, 'error');
      return;
    }

    if (config.uploadedFiles.length !== 2) {
      showMessage(`請上傳 2 個檔案：1個 .txt 檔案 + 1個 .docx 檔案`, 'error');
      return;
    }

    // 檢查檔案類型
    const hasText = config.uploadedFiles.some(file => file.name.toLowerCase().endsWith('.txt'));
    const hasDocx = config.uploadedFiles.some(file => file.name.toLowerCase().endsWith('.docx'));

    if (!hasText || !hasDocx) {
      showMessage(`請確保上傳 1個 .txt 檔案和 1個 .docx 檔案`, 'error');
      return;
    }

    const btnId = mode === 'taiex' ? 'taiexRecognizeBtn' : 'ussRecognizeBtn';
    const button = document.getElementById(btnId);
    const originalText = button.innerHTML;
    
    // 初始狀態
    button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 提交任務中...`;
    button.disabled = true;

    const formData = new FormData();
    config.uploadedFiles.forEach(file => formData.append("files", file));

    const endpoint = mode === 'taiex' ? 'stock_index_async' : 'uss_index_async';
    
    try {
      // 第一步：提交任務
      const response = await fetch(`https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7/${endpoint}`, {
        method: "POST",
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const taskInfo = await response.json();
      
      if (!taskInfo.task_id) {
        throw new Error('未收到任務ID');
      }
      
      // 顯示任務已提交
      showMessage(`${mode === 'taiex' ? '台股' : '美股'}任務已提交，開始處理...`, 'success');
      
      // 第二步：輪詢任務狀態
      const result = await pollTaskStatus(taskInfo.task_id, mode, button);
      
      // 第三步：應用結果
      config.defaultDatas = result;
      applyRecognitionResult(mode);

      showMessage(`${mode === 'taiex' ? '台股' : '美股'}辨識成功`, 'success');

      // 辨識完成後重新載入連結資料
      setTimeout(async () => {
        await loadLinkData();
        showMessage('連結資料已更新', 'info');
      }, 2000); // 等待2秒讓後端有時間保存連結資料
      
    } catch (err) {
      showMessage(`${mode === 'taiex' ? '台股' : '美股'}辨識失敗: ${err.message}`, 'error');
      console.error('辨識錯誤:', err);
    } finally {
      button.innerHTML = originalText;
      button.disabled = false;
    }
}

// 輪詢任務狀態的函數
async function pollTaskStatus(taskId, mode, button) {
  const maxAttempts = 120; // 最多輪詢120次 (10分鐘)
  const pollInterval = 5000; // 每5秒輪詢一次
  let attempts = 0;
  
  return new Promise((resolve, reject) => {
    const poll = async () => {
      attempts++;
      
      if (attempts > maxAttempts) {
        reject(new Error('任務超時，請重試'));
        return;
      }
      
      try {
        const statusResponse = await fetch(`https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7/task_status/${taskId}`);
        
        if (!statusResponse.ok) {
          throw new Error(`狀態查詢失敗: ${statusResponse.status}`);
        }
        
        const status = await statusResponse.json();
        
        // 更新按鈕狀態
        updateButtonProgress(button, status, mode);
        
        switch (status.status) {
          case 'completed':
            // 任務完成
            if (status.result) {
              resolve(status.result);
            } else {
              reject(new Error('任務完成但沒有結果'));
            }
            break;
            
          case 'failed':
            // 任務失敗
            reject(new Error(status.error || '任務執行失敗'));
            break;
            
          case 'processing':
          case 'queued':
            // 任務仍在處理中，繼續輪詢
            setTimeout(poll, pollInterval);
            break;
            
          default:
            // 未知狀態
            reject(new Error(`未知的任務狀態: ${status.status}`));
        }
        
      } catch (error) {
        console.error('輪詢錯誤:', error);
        // 網絡錯誤，繼續嘗試
        setTimeout(poll, pollInterval);
      }
    };
    
    // 開始輪詢
    poll();
  });
}

// 更新按鈕進度顯示
function updateButtonProgress(button, status, mode) {
  const modeText = mode === 'taiex' ? '台股' : '美股';
  const progress = status.progress || 0;
  
  switch (status.status) {
    case 'queued':
      button.innerHTML = `<i class="fas fa-clock"></i> 排隊中... (${modeText})`;
      break;
    case 'processing':
      button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 處理中 ${progress}% (${modeText})`;
      break;
    case 'completed':
      button.innerHTML = `<i class="fas fa-check"></i> 處理完成 (${modeText})`;
      break;
    case 'failed':
      button.innerHTML = `<i class="fas fa-times"></i> 處理失敗 (${modeText})`;
      break;
    default:
      button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${status.status} (${modeText})`;
  }
}

  function applyRecognitionResult(targetMode = null) {
    const mode = targetMode || currentMode;
    const config = configs[mode];

    config.defaultDatas.slice(0, 4).forEach((list, index) => {
      processCanvasData(mode, config, list, index);
    });

    if (config.defaultDatas.length > 4) {
      const fifthData = config.defaultDatas[4];

      if (Array.isArray(fifthData) && fifthData.length > 0 && fifthData[0].image_index !== undefined) {
        processNewFormatVariants(mode, config, fifthData);
      } else {
        processOldFormatVariants(mode, config, fifthData);
      }
    }

    // 辨識結果應用後，重新載入連結資料以顯示最新的網址
    setTimeout(async () => {
      try {
        await loadLinkData();
        console.log('連結資料已重新載入');
      } catch (error) {
        console.error('重新載入連結資料失敗:', error);
      }
    }, 1000); // 等待1秒讓後端有時間保存連結資料
  }

  function processNewFormatVariants(mode, config, fifthData) {
    let variants = fifthData;
    if (Array.isArray(fifthData) && fifthData.length > 0 && Array.isArray(fifthData[0])) {
      variants = fifthData[0];
    }

    for (let i = 0; i < 4 && i < variants.length; i++) {
      const canvasIndex = 4 + i;
      const variant = variants[i];
      
      if (variant && variant.data) {
        if (variant.image_index !== undefined) {
          config.canvasImageIndex[canvasIndex] = variant.image_index;
        }
        processCanvasData(mode, config, variant.data, canvasIndex);
      }
    }
  }
  
  function processOldFormatVariants(mode, config, fifthData) {
    for (let i = 0; i < 4; i++) {
      const canvasIndex = 4 + i;
      
      // 根據模式設定不同的image_index分配策略
      let expectedImageIndex;
      if (mode === 'taiex') {
        // 台股：0,1,0,1 的循環
        expectedImageIndex = (i % 2);
      } else if (mode === 'uss') {
        // 美股：0,1,2,0 的分配
        if (i === 0 || i === 3) {
          expectedImageIndex = 0; // 預設圖
        } else if (i === 1) {
          expectedImageIndex = 1; // 變體1
        } else if (i === 2) {
          expectedImageIndex = 2; // 變體2
        }
      }
      config.canvasImageIndex[canvasIndex] = expectedImageIndex;
      
      const dataForCanvas = fifthData.filter(item => {
        if (item.image_index !== undefined) {
          return item.image_index === expectedImageIndex;
        }
        return true;
      });
      processCanvasData(mode, config, dataForCanvas, canvasIndex);
    }
  }

  function processCanvasData(mode, config, list, index) {
    const objects = list.map(item => {
      if (item.type === "text") {
        const isFirstCanvas = index === 0;
        const isThirdCanvas = index === 2;
        const isFourthCanvas = index === 3;
        const isFifthToEighthCanvas = index >= 4;
  
        

        if (isFirstCanvas) {
          return new fabric.Text(item.value, {
            left: item.x,
            top: item.y,
            fontSize: item.fontSize,
            fill: item.fill,
            selectable: true,
            originX: "right",
            originY: "center",
            fontFamily: "Noto Sans TC",
            fontWeight: 500
          });
        }
  
        if (isThirdCanvas) {
          const maxWidth = 270;
          const maxLines = 13;
          const fontSize = item.fontSize || 24;
          const fontFamily = "Noto Sans TC";
          const fontWeight = 500;
          const lineHeight = 1.2
        
          const originalText = item.value;
          const testBox = new fabric.Textbox('', {
            width: maxWidth,
            fontSize: fontSize,
            fontFamily: fontFamily,
            fontWeight: fontWeight,
            splitByGrapheme: true,
            lineHeight: lineHeight 
          });
        
          let visibleText = '';
        
          for (let i = 1; i <= originalText.length; i++) {
            const current = originalText.slice(0, i);
            testBox.text = current;
        
            canvas.add(testBox);
            canvas.renderAll();
            const lines = testBox._textLines;
            canvas.remove(testBox);
        
            if (lines.length > maxLines) break;
        
            visibleText = current;
          }
        
          // 判斷是否截斷過
          const wasTruncated = visibleText.length < originalText.length;
        
          if (wasTruncated) {
            const shortened = visibleText.slice(0, -2) + '...';
            visibleText = shortened;
          }
        
          const firstTextItem = list.find(i => i.type === "text");
          const baseX = firstTextItem?.x || item.x;
          const baseY = firstTextItem?.y || item.y;
  
          return new fabric.Textbox(visibleText, {
            left: baseX,
            top: baseY,
            width: maxWidth,
            fontSize: fontSize,
            fill: item.fill,
            selectable: true,
            editable: true,
            originX: "left",
            originY: "top",
            fontFamily: fontFamily,
            fontWeight: fontWeight,
            splitByGrapheme: true,
            lineHeight: lineHeight 
          });
        }

        if (isFourthCanvas) {
          const fontSize = item.fontSize || 24;

          let useSpecialPositioning = false;
          if (mode === 'uss' && item.fill === "#FFFFFF") {
            useSpecialPositioning = true;
          } else if (mode === 'taiex' && item.fill === "#000079") {
            useSpecialPositioning = true;
          }
          
          if (useSpecialPositioning) {
            return new fabric.Text(item.value, {
              left: item.x,
              top: item.y,
              fontSize: fontSize,
              fill: item.fill,
              selectable: true,
              originX: "center",
              originY: "center",
              fontFamily: "Noto Sans TC",
              fontWeight: 400
            });
          } else {
            const charWidthEstimate = fontSize;
            const maxChars = 12;
            const estimatedWidth = charWidthEstimate * maxChars;
            
            return new fabric.Textbox(item.value, {
              left: item.x,
              top: item.y,
              width: estimatedWidth,
              fontSize: fontSize,
              fill: item.fill,
              selectable: true,
              editable: true,
              originX: "left",
              originY: "top",
              fontFamily: "Noto Sans TC",
              fontWeight: 500,
              splitByGrapheme: true,
              lineHeight: 1.2
            });
          }
        }
        

        if (isFifthToEighthCanvas) {
          const fontSize = item.fontSize || 24;
          let maxChars, estimatedWidth, originX, originY;
          
          if (fontSize === 32) {
            maxChars = 8;
            estimatedWidth = fontSize * maxChars * 1.2;
            originX = "center";
            originY = "center";
          } else if (fontSize === 20) {
            maxChars = 12;
            estimatedWidth = fontSize * maxChars * 1.1;
            originX = "left";
            originY = "top";
          } else {
            maxChars = 10;
            estimatedWidth = fontSize * maxChars * 1.1;
            originX = "center";
            originY = "center";
          }
          
          // 根據 fontSize 決定是否添加 textAlign 屬性
          const textboxProps = {
            left: item.x,
            top: item.y,
            width: estimatedWidth,
            fontSize: fontSize,
            fill: item.fill,
            selectable: true,
            editable: true,
            originX: originX,
            originY: originY,
            fontFamily: "Noto Sans TC",
            fontWeight: 500,
            splitByGrapheme: true,
            breakWords: false
          };
          
          // 只有當 fontSize === 32 時才添加 textAlign: "center"
          if (fontSize === 32) {
            textboxProps.textAlign = "center";
            textboxProps.lineHeight = 1.2; // 適當的行高，確保兩行文字有合適的間距
          }
          
          return new fabric.Textbox(item.value, textboxProps);
        }

        return new fabric.Textbox(item.value, {
          left: item.x,
          top: item.y,
          fontSize: item.fontSize,
          fill: item.fill,
          selectable: true,
          editable: true,
          originX: "center",
          originY: "center",
          fontFamily: "Noto Sans TC",
          fontWeight: 500
        });
      }
  
      if (item.type === "image") {
        let imagePath = null;
        
        if (mode === 'taiex') {
          if (item.value === 'up') imagePath = config.imageMap.up;
          else if (item.value === 'down') imagePath = config.imageMap.down;
        } else if (mode === 'uss') {
          if (item.value === 'us_up') imagePath = config.imageMap.us_up;
          else if (item.value === 'us_down') imagePath = config.imageMap.us_down;
        }
        
        if (imagePath) {
          return new Promise(resolve => {
            fabric.Image.fromURL(imagePath, function(img) {
              const desiredWidth = 13;
              const desiredHeight = 11;
  
              img.scaleToWidth(desiredWidth);
              img.scaleToHeight(desiredHeight);
  
              img.set({
                left: item.x,
                top: item.y,
                selectable: true
              });
  
              resolve(img);
            }, { crossOrigin: 'anonymous' });
          });
        }
      }
  
      return null;
    });
  
    Promise.all(objects).then(resolvedObjects => {
      config.canvasData[index] = resolvedObjects.filter(obj => obj !== null);
      if (mode === currentMode && index === currentIndex) {
        loadCanvas(currentIndex);
      }
    });
  }

  function showMessage(text, type) {
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
      existingMessage.remove();
    }

    const message = document.createElement('div');
    message.className = `message ${type}`;
    message.textContent = text;
    document.body.appendChild(message);

    setTimeout(() => {
      message.classList.add('show');
    }, 100);

    setTimeout(() => {
      message.classList.remove('show');
      setTimeout(() => {
        if (message.parentNode) {
          message.remove();
        }
      }, 300);
    }, 3000);
  }
  
  // 下載連結資料功能
  async function downloadLinks(type) {
    try {
      // 獲取當前日期，格式為 YYYYMMDD
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const dateFolder = `${year}${month}${day}`;
      
      // 顯示加載中訊息
      showMessage('正在下載連結資料...', 'success');
      
      // 使用 API 端點獲取連結資料
      const apiBaseUrl = 'https://bot.agatha-ai.com/flowise/eda63afd-8051-4aba-8eb0-e527fa6937a7';
      
      // 下載台股連結
      if (type === 'taiex' || type === 'all') {
        fetch(`${apiBaseUrl}/get_url_json?type=taiex&date=${dateFolder}`)
          .then(response => {
            if (!response.ok) {
              throw new Error(`API 請求失敗，狀態碼: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            downloadJsonFile(data, `${dateFolder}_台股.json`);
            showMessage('台股連結下載成功', 'success');
          })
          .catch(error => {
            console.error(error);
            showMessage('下載台股連結失敗', 'error');
          });
      }
      
      // 下載美股連結
      if (type === 'uss' || type === 'all') {
        fetch(`${apiBaseUrl}/get_url_json?type=us&date=${dateFolder}`)
          .then(response => {
            if (!response.ok) {
              throw new Error(`API 請求失敗，狀態碼: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            downloadJsonFile(data, `${dateFolder}_美股.json`);
            showMessage('美股連結下載成功', 'success');
          })
          .catch(error => {
            console.error(error);
            showMessage('下載美股連結失敗', 'error');
          });
      }
    } catch (error) {
      console.error(error);
      showMessage('下載連結資料失敗', 'error');
    }
  }
  
  // 下載 JSON 檔案
  function downloadJsonFile(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  loadCanvas(currentIndex);

  // 載入連結資料
  loadLinkData();

  checkTodayJson();

  // 初始化檔案狀態顯示
  updateFileStatus('taiex');
  updateFileStatus('uss');

  // 更改密碼彈出視窗功能
  function showChangePasswordModal() {
    // 創建彈出視窗HTML
    const modalHTML = `
      <div id="changePasswordModal" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      ">
        <div style="
          background: white;
          border-radius: 8px;
          padding: 30px;
          width: 90%;
          max-width: 400px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        ">
          <h3 style="margin: 0 0 20px 0; color: #333; text-align: center;">
            <i class="fas fa-key"></i> 更改密碼
          </h3>
          <form id="changePasswordModalForm">
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: 500;">當前密碼</label>
              <input type="password" id="modalCurrentPassword" required style="
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
              ">
            </div>
            <div style="margin-bottom: 15px;">
              <label style="display: block; margin-bottom: 5px; font-weight: 500;">新密碼</label>
              <input type="password" id="modalNewPassword" required minlength="6" style="
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
              ">
              <small style="color: #666; font-size: 12px;">密碼長度至少6個字符</small>
            </div>
            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 5px; font-weight: 500;">確認新密碼</label>
              <input type="password" id="modalConfirmPassword" required style="
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
              ">
            </div>
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
              <button type="button" onclick="closeChangePasswordModal()" style="
                padding: 10px 20px;
                border: 1px solid #ddd;
                background: white;
                border-radius: 4px;
                cursor: pointer;
              ">取消</button>
              <button type="submit" style="
                padding: 10px 20px;
                border: none;
                background: #3498db;
                color: white;
                border-radius: 4px;
                cursor: pointer;
              ">
                <i class="fas fa-save"></i> 更改密碼
              </button>
            </div>
          </form>
        </div>
      </div>
    `;

    // 添加到頁面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 添加表單提交事件
    document.getElementById('changePasswordModalForm').addEventListener('submit', handleChangePassword);
  }

  function closeChangePasswordModal() {
    const modal = document.getElementById('changePasswordModal');
    if (modal) {
      modal.remove();
    }
  }

  async function handleChangePassword(e) {
    e.preventDefault();

    const currentPassword = document.getElementById('modalCurrentPassword').value;
    const newPassword = document.getElementById('modalNewPassword').value;
    const confirmPassword = document.getElementById('modalConfirmPassword').value;

    // 驗證新密碼
    if (newPassword.length < 6) {
      showMessage('新密碼長度至少需要6個字符', 'error');
      return;
    }

    if (newPassword !== confirmPassword) {
      showMessage('新密碼與確認密碼不一致', 'error');
      return;
    }

    if (currentPassword === newPassword) {
      showMessage('新密碼不能與當前密碼相同', 'error');
      return;
    }

    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更改中...';

    try {
      let response;

      try {
        response = await fetch('http://localhost:6536/change-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
            collection: "KG_Stack",
            username: currentUser.username,
            currentPassword,
            newPassword
          })
        });
      } catch (localError) {
        // 如果本地API失敗，嘗試遠程API
        response = await fetch('https://bot.agatha-ai.com/flowise/24ced3c2-063e-41ad-9558-26f31727753c/change-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            dbUri: "mongodb+srv://emergence:<EMAIL>/?retryWrites=true&w=majority&appName=ChatBot",
            collection: "KG_Stack",
            username: currentUser.username,
            currentPassword,
            newPassword
          })
        });
      }

      const result = await response.json();

      if (response.ok && result.success) {
        // 記錄更改密碼事件
        try {
          if (window.Logger && typeof Logger.logChangePassword === 'function') {
            await Logger.logChangePassword();
          }
        } catch (logError) {
          // 記錄失敗不影響主要功能
        }

        showMessage('密碼更改成功！請重新登入', 'success');
        closeChangePasswordModal();

        // 3秒後自動登出
        setTimeout(() => {
          sessionStorage.removeItem('loginStatus');
          window.location.href = 'login.html';
        }, 3000);
      } else {
        showMessage(result.message || '密碼更改失敗', 'error');
      }

    } catch (error) {
      showMessage('密碼更改失敗', 'error');
    } finally {
      submitBtn.disabled = false;
      submitBtn.innerHTML = originalText;
    }
  }
</script>

</body>
</html>